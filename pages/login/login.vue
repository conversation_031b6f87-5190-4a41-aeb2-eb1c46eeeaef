<template>
	<view class="page">
		<view class="logo">
			<image src="../../static/logo.png" mode=""></image>
		</view>
		<!-- 填写区 -->
		<block v-if="!loginSuccess">
		<view class="input-info">
			<view class="info">
				<input type="tel" maxlength="11" v-model="form.phone" placeholder="手机号">
				<view class="more"></view>
			</view>
			<view class="info" :style="isLoginWay?'':'display: none'">
				<input type="tel" v-model="form.code" maxlength="6" placeholder="请输入验证码">
				<view class="more">
					<text class="mo" @click="getCode">{{getCodeTxt}}</text>
					<!-- <text class="mo" style="display: none">59秒后重试</text> -->
				</view>
			</view>
			<view class="info" :style="isLoginWay?'display: none':''">
				<input :password='!isPassword' v-model="form.password" maxlength="26" placeholder="请输入密码">
				<view class="more">
					<text class="iconfont" :class="isPassword?'icon-eye-on':'icon-eye-off'"
						@click="isPassword = !isPassword"></text>
					<text class="mo" @click="onLoginCut">忘记密码</text>
				</view>
			</view>
		</view>
		<!-- 按钮 -->
		<view class="btn-info">
			<view class="btn" :style="isLogin?'opacity:1':'opacity:0.4'" @click="isLogin?onLogin():''">
				<text>登录</text>
			</view>
		</view>
		<view class="flex loginContract flexItemCenter btn-info">
			<checkbox @click="agree" value="1" />我已阅读并同意<view class="green" @click="gotoArticle(9)">
				《注册协议》</view>和<view class="green" @click="gotoArticle(6)">《隐私政策》</view>
		</view>
		<!-- 操作 -->
		<view class="operation" style="justify-content: center;">
			<text @click="onLoginCut">{{isLoginWay?'手机号密码登录':'短信验证码登录'}}</text>
			<!-- <text @click="onRegister">新用户注册</text> -->
		</view>
		</block>
		<!-- 其他方式登录 -->
		<block v-if="newUser == 1">
			<view class="other-ways">
				<text>下载安卓APP</text>
			</view>
			<!-- 登录方式 -->
			<view class="login-way">
				<view class="way" @click="gotoDown">
					<image :src="api+'/static/code.png'" mode="aspectFit"></image>
					<text>点击或扫码下载</text>
				</view>
				<view class="way" @click="gotoIndex">
					<image src="/static/nav2/5.png" mode="aspectFit"></image>
					<text>去H5首页</text>
				</view>
			</view>
		</block>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isLogin: false,
				loginSuccess:false,
				api:getApp().globalData.apiUrl,
				isLoginWay: true,
				newUser:0,
				isPassword: false,
				getCodeTxt: '获取验证码',
				xieyiArr: [{
						title: '用户协议',
						id: 12
					},
					{
						title: '隐私声明',
						id: 13
					},
				],
				timeOut: 60,
				gotoTimeOut: true,
				agreeXY: false,
				fromID: '',
				// 表单
				form: {
					phone: '',
					code: '',
					password: '',
					password2: '',
				},
			};
		},
		onLoad(option) {

			if(option.userCode){
				this.isLoginWay=false
				this.onLoginCut()
			}else{
				this.isLoginWay=true
				this.onLoginCut()
			}
			this.fromID = uni.getStorageSync('fromID')
			if (!this.fromID) {
				this.fromID = option.userCode
				if (this.fromID) {
					uni.setStorageSync("fromID", this.fromID)

				}
			}
		},
		methods: {
			gotoIndex(){
				uni.switchTab({
					url:"/pages/home/<USER>"
				})
			},
			gotoDown(){
				uni.navigateTo({
					url:"/pages/appDown/appDown"
				})
			},
			agree(e) {
				this.agreeXY = !this.agreeXY
			},
			gotoArticle($id) {
				uni.navigateTo({
					url: "/pages/ArticleDetails/ArticleDetails?id=" + $id
				})
			},
			onRegister() {
				uni.navigateTo({
					url: '/pages/register/register'
				})
			},
			/**
			 * 登录切换
			 */
			onLoginCut() {
				this.isLoginWay = !this.isLoginWay;
				// 验证码
				if (this.isLoginWay) {
					this.isLogin = this.form.code && this.form.phone ? true : false;
					this.form.password = ''
				}
				// 账号密码
				if (!this.isLoginWay) {
					this.isLogin = this.form.password && this.form.phone ? true : false;
					this.form.code = ''
				}
			},
			/**
			 * 登录点击
			 */
			onLogin() {
				if (this.agreeXY == false) {
					uni.showToast({
						title: "查看并同意协议"
					})
					return false
				}
				this.$http.get('mobileLogin', {
					mobile: this.form.phone,
					code: this.form.code,
					fromID: this.fromID,
					password:this.form.password,
				}).then(res => {
					if (res.code == 0) {
						uni.showToast({
							title: "登录成功"
						})
						uni.setStorageSync("token", res.msg)
						getApp().globalData.token = res.msg
						setTimeout(() => {
							if(res.data.new == 1){
								// #ifdef APP
								uni.switchTab({
									url: "/pages/home/<USER>"
								})
								// #endif
								// #ifndef APP
								var ua = window.navigator.userAgent.toLowerCase();
								const isIOS = /iphone|ipad|ipod/i.test(ua);
								if(!isIOS){
									this.newUser=1
									this.loginSuccess=true
								}else{
									uni.switchTab({
										url: "/pages/home/<USER>"
									})
								}
								// #endif
							}else{
								uni.switchTab({
									url: "/pages/home/<USER>"
								})
							}
						}, 1000)
					} else {
						uni.showToast({
							title: res.msg
						})
					}
				})
			},
			daojishi() {
				setTimeout(() => {
					this.getCodeTxt = this.timeOut + "秒后获取"
					this.timeOut--
					if (this.timeOut > 0) {
						this.daojishi()
					} else {
						this.getCodeTxt = '获取验证码'
						this.timeOut = 60
						this.gotoTimeOut=false
					}
				}, 1000)
			},
			getCode() {
				var that = this
        if (that.form.phone.length === 0){
          uni.showToast({
            title: "请输入手机号",
            icon: "none",
          })
          return false
        }
        if (that.form.phone.length !== 11){
          uni.showToast({
						title: "手机号错误",
            icon: "none",
					})
					return false
        }
				if (that.agreeXY === false) {
					uni.showToast({
						title: "查看并同意协议",
            icon: "none",
					})
					return false
				}
				if (that.timeOut !== 60) {
					uni.showToast({
						title: "倒计时结束获取"
					})
					return false
				}
				uni.showLoading({
					title: '获取中...'
				})
				if (that.gotoTimeOut === true) {

					that.gotoTimeOut = false
					setTimeout(() => {
						this.$http.get('getCode2', {
							mobile: that.form.phone
						}).then(res => {
							uni.hideLoading()
							if (res.code === 0) {
								uni.showToast({
									title: '发送成功'
								})
								that.daojishi()
							} else {
								that.gotoTimeOut = true
								uni.showToast({
									title: res.msg,
                  icon: "none",
								})
							}
						})
					}, 1000)
					// that.daojishi()
				} else {
					uni.showToast({
						title: that.timeOut + "秒后获取",
            icon: "none",
					})
				}
			}
		},
		watch: {
			form: {
				handler(newValue, oldValue) {
					// 验证码
					if (this.isLoginWay) {
						this.isLogin = newValue.code && newValue.phone ? true : false;
					}
					// 账号密码
					if (!this.isLoginWay) {
						this.isLogin = newValue.password && newValue.phone ? true : false;
					}
				},
				deep: true
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'login.scss';
</style>
